#include "mainwindow.h"
#include <QPainter>
#include <QtMath>
#include <QDebug>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
{
    // 数据初始化（和你的Python数据类似）
    curves = {
        {QPointF(9794352.00, -0.67), QPointF(0.00, 0.00), -10454268.67},
        {QPointF(0.00, 0.00), QPointF(4778222.00, 8779192.67), -10454268.67},
        {QPointF(4778222.00, 8779192.67), QPointF(181229.33, 17438526.00), 0.00},
        {QPointF(181229.33, 17438526.00), QPointF(180452.00, 17449800.00), 0.00},
        {QPointF(180452.00, 17449800.00), QPointF(9773872.67, 17449799.33), 0.00},
        {QPointF(9773872.67, 17449799.33), QPointF(9794352.00, -0.67), 0.00}
    };
    resize(800, 1200);
}

MainWindow::~MainWindow() {}

void MainWindow::paintEvent(QPaintEvent *)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    painter.setPen(QPen(Qt::black, 2));

    // 坐标缩放和平移（根据实际数据调整）
    double scale = 0.00005;
    double offsetX = 50;
    double offsetY = 1000;

    for (const auto& seg : curves) {
        QPointF p0 = QPointF(seg.p0.x() * scale + offsetX, offsetY - seg.p0.y() * scale);
        QPointF p1 = QPointF(seg.p1.x() * scale + offsetX, offsetY - seg.p1.y() * scale);
        if (qFuzzyIsNull(seg.radius)) {
            // 直线段
            painter.drawLine(p0, p1);
        } else {
            // 圆弧段
            // 计算圆心
            double x0 = seg.p0.x(), y0 = seg.p0.y();
            double x1 = seg.p1.x(), y1 = seg.p1.y();
            double r = seg.radius;
            double chord_length = std::hypot(x1 - x0, y1 - y0);
            if (std::abs(r) < chord_length / 2) {
                painter.drawLine(p0, p1);
                continue;
            }
            double mid_x = (x0 + x1) / 2;
            double mid_y = (y0 + y1) / 2;
            double abs_r = std::abs(r);
            double h = std::sqrt(abs_r * abs_r - (chord_length / 2) * (chord_length / 2));
            double chord_dx = x1 - x0;
            double chord_dy = y1 - y0;
            double perp_dx = -chord_dy / chord_length;
            double perp_dy = chord_dx / chord_length;
            double center_x, center_y;
            if (r < 0) {
                center_x = mid_x + h * perp_dx;
                center_y = mid_y + h * perp_dy;
            } else {
                center_x = mid_x - h * perp_dx;
                center_y = mid_y - h * perp_dy;
            }
            // 计算起止角
            double start_angle = std::atan2(y0 - center_y, x0 - center_x);
            double end_angle = std::atan2(y1 - center_y, x1 - center_x);
            double angle_diff = end_angle - start_angle;
            if (angle_diff <= 0) angle_diff += 2 * M_PI;

            // Qt 需要角度，且单位为1/16度
            double cx = center_x * scale + offsetX;
            double cy = offsetY - center_y * scale;
            double radius_px = abs_r * scale;
            QRectF rect(cx - radius_px, cy - radius_px, 2 * radius_px, 2 * radius_px);
            double start_deg = -start_angle * 180.0 / M_PI;
            double span_deg = -angle_diff * 180.0 / M_PI;
            painter.drawArc(rect, int(start_deg * 16), int(span_deg * 16));
        }
    }
} 